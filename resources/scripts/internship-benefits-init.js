class InternshipBenefitsLine {
    constructor(container) {
        this.container = container;
        this.items = container.querySelectorAll('.benefit-line-item');
        this.descriptions = container.querySelectorAll('.benefit-description');
        this.progressLines = container.querySelectorAll('.progress-line');
        this.progressFills = container.querySelectorAll('.progress-fill');

        this.currentIndex = 0;
        this.isAutoProgressing = true;
        this.isTransitioning = false;
        this.progressTimer = null;
        this.transitionTimer = null;

        this.FILL_DURATION = 4500; // 4.5 seconds
        this.TRANSITION_DURATION = 700; // 0.7 seconds
        this.SPACING = 32; // 32px spacing between items (mb-8)
        this.ICON_SIZE = 36; // w-9 h-9 = 36px
        this.LINE_TOP_OFFSET = 48; // top-12 = 48px (distance from icon top to line start)

        // Calculate proper heights
        this.CLOSED_ITEM_HEIGHT = 105; // Content height for closed items (margin is additional)
        // For closed items: content height - line offset = line height
        this.CLOSED_LINE_HEIGHT = this.CLOSED_ITEM_HEIGHT - this.LINE_TOP_OFFSET; // 105 - 48 = 57px

        this.init();
    }

    init() {
        if (!this.items.length) return;

        // Set up initial state
        this.calculateDimensions();
        this.initializeFadeStates();
        this.showBenefit(0, false);
        this.startAutoProgress();

        // Event listeners
        this.setupEventListeners();
    }

    calculateDimensions() {
        this.items.forEach((item, index) => {
            const description = item.querySelector('.benefit-description');
            const line = item.querySelector('.progress-line');

            if (description) {
                // Temporarily show content to measure it
                const wasHidden = description.style.maxHeight === '0px' || description.style.maxHeight === '0';
                if (wasHidden) {
                    description.style.maxHeight = 'none';
                    description.style.visibility = 'hidden';
                }

                const contentHeight = description.scrollHeight;
                // For expanded items: line should extend through the content but stop before next item
                // We need to measure the actual distance from line start to next item and subtract spacing
                const nextItem = this.items[index + 1];
                let expandedLineHeight = this.CLOSED_LINE_HEIGHT;

                if (nextItem) {
                    // Temporarily expand this item to measure the distance
                    const wasHidden = description.style.maxHeight === '0px' || description.style.maxHeight === '0';
                    if (wasHidden) {
                        description.style.maxHeight = `${contentHeight}px`;
                    }

                    // Measure distance from current line start to next item
                    const currentItemRect = item.getBoundingClientRect();
                    const nextItemRect = nextItem.getBoundingClientRect();
                    const distanceToNext = nextItemRect.top - (currentItemRect.top + this.LINE_TOP_OFFSET);
                    expandedLineHeight = Math.max(this.CLOSED_LINE_HEIGHT, distanceToNext - this.SPACING);

                    // Restore state
                    if (wasHidden) {
                        description.style.maxHeight = '0';
                    }
                } else {
                    // Last item: just use content height
                    expandedLineHeight = Math.max(this.CLOSED_LINE_HEIGHT, contentHeight);
                }

                // Store dimensions as CSS custom properties
                description.style.setProperty('--content-height', `${contentHeight}px`);
                if (line) {
                    line.style.setProperty('--line-height-expanded', `${expandedLineHeight}px`);
                    line.style.setProperty('--line-height-closed', `${this.CLOSED_LINE_HEIGHT}px`);
                }

                // Restore original state
                if (wasHidden) {
                    description.style.maxHeight = '0';
                    description.style.visibility = '';
                }
            }
        });
    }

    initializeFadeStates() {
        // Set initial fade states for all content
        this.descriptions.forEach((desc, index) => {
            const inner = desc.querySelector('.description-inner');
            if (inner) {
                if (index === 0) {
                    // First item should be visible
                    inner.classList.add('fade-in');
                    inner.classList.remove('fade-out');
                } else {
                    // Other items should be hidden
                    inner.classList.add('fade-out');
                    inner.classList.remove('fade-in');
                }
            }
        });
    }

    showBenefit(index, animate = true, fadeIn = true) {
        console.log(`📏 showBenefit called: index=${index}, animate=${animate}, fadeIn=${fadeIn}, isTransitioning=${this.isTransitioning}`);

        if (this.isTransitioning && animate) {
            console.log('❌ showBenefit blocked: transitioning and animate=true');
            return;
        }

        console.log(`📏 Setting currentIndex from ${this.currentIndex} to ${index}`);
        this.currentIndex = index;

        // Update descriptions
        this.descriptions.forEach((desc, i) => {
            if (i === index) {
                desc.style.maxHeight = desc.style.getPropertyValue('--content-height') || '200px';
                // If this is the initial load or we want to fade in, handle content visibility
                if (fadeIn) {
                    this.fadeInContent(i);
                }
            } else {
                desc.style.maxHeight = '0';
                // Ensure other content is faded out
                this.fadeOutContent(i);
            }
        });

        // Update line heights
        this.progressLines.forEach((line, i) => {
            if (i === index) {
                // Current item: expand line to content height
                line.style.height = line.style.getPropertyValue('--line-height-expanded') || `${this.CLOSED_LINE_HEIGHT}px`;
            } else {
                // Other items: closed height
                line.style.height = line.style.getPropertyValue('--line-height-closed') || `${this.CLOSED_LINE_HEIGHT}px`;
            }
        });

        // Update progress fills
        this.progressFills.forEach((fill, i) => {
            if (i < index) {
                // Completed items: fully filled
                fill.style.height = '100%';
            } else {
                // Current and future items: empty
                fill.style.height = '0%';
            }
        });
    }

    startAutoProgress() {
        if (!this.isAutoProgressing || this.isTransitioning) return;

        this.clearTimers();

        const currentFill = this.progressFills[this.currentIndex];
        if (currentFill) {
            // Start filling the current line
            currentFill.style.height = '100%';

            // Schedule transition to next benefit
            this.progressTimer = setTimeout(() => {
                if (this.isAutoProgressing && !this.isTransitioning) {
                    const nextIndex = (this.currentIndex + 1) % this.items.length;
                    this.transitionToNext(nextIndex);
                }
            }, this.FILL_DURATION);
        }
    }

    transitionToNext(nextIndex) {
        if (this.isTransitioning) return;

        this.isTransitioning = true;
        this.clearTimers();

        // Step 1: Fade out current content text
        this.fadeOutContent(this.currentIndex);

        // Step 2: After fade out completes, close height and shrink line
        setTimeout(() => {
            // Close current description
            const currentDesc = this.descriptions[this.currentIndex];
            if (currentDesc) {
                currentDesc.style.maxHeight = '0';
            }

            // Shrink current line to closed height
            const currentLine = this.progressLines[this.currentIndex];
            if (currentLine) {
                currentLine.style.height = `${this.CLOSED_LINE_HEIGHT}px`;
            }

            // Step 3: After closing animation completes, open next benefit
            this.transitionTimer = setTimeout(() => {
                // Open next benefit (height only, no fade in yet)
                this.showBenefit(nextIndex, false, false);

                // Step 4: After height animation completes, fade in new content
                setTimeout(() => {
                    this.fadeInContent(nextIndex);

                    // Step 5: After fade in completes, mark transition as done
                    setTimeout(() => {
                        this.isTransitioning = false;

                        // Continue auto-progression after a brief pause
                        setTimeout(() => {
                            this.startAutoProgress();
                        }, 300);
                    }, 350); // Wait for fade in to complete
                }, this.TRANSITION_DURATION);
            }, this.TRANSITION_DURATION);
        }, 350); // Wait for fade out to complete
    }

    fadeOutContent(index) {
        console.log(`🌫️ fadeOutContent called for index ${index}`);
        const desc = this.descriptions[index];
        if (desc) {
            const inner = desc.querySelector('.description-inner');
            if (inner) {
                console.log(`🌫️ Adding fade-out class to item ${index}`);
                inner.classList.remove('fade-in');
                inner.classList.add('fade-out');
            } else {
                console.log(`❌ No .description-inner found for item ${index}`);
            }
        } else {
            console.log(`❌ No description found for item ${index}`);
        }
    }

    fadeInContent(index) {
        console.log(`✨ fadeInContent called for index ${index}`);
        const desc = this.descriptions[index];
        if (desc) {
            const inner = desc.querySelector('.description-inner');
            if (inner) {
                console.log(`✨ Adding fade-in class to item ${index}`);
                inner.classList.remove('fade-out');
                inner.classList.add('fade-in');
            } else {
                console.log(`❌ No .description-inner found for item ${index}`);
            }
        } else {
            console.log(`❌ No description found for item ${index}`);
        }
    }

    stopAutoProgress() {
        this.isAutoProgressing = false;
        this.clearTimers();
    }

    resumeAutoProgress() {
        this.isAutoProgressing = true;
        this.startAutoProgress();
    }

    clearTimers() {
        if (this.progressTimer) {
            clearTimeout(this.progressTimer);
            this.progressTimer = null;
        }
        if (this.transitionTimer) {
            clearTimeout(this.transitionTimer);
            this.transitionTimer = null;
        }
    }

    setupEventListeners() {
        // Click handlers for manual navigation
        this.items.forEach((item, index) => {
            item.addEventListener('click', () => {
                console.log(`🖱️ Clicked on item ${index}, current index: ${this.currentIndex}, isTransitioning: ${this.isTransitioning}`);

                if (this.isTransitioning) {
                    console.log('❌ Blocked: Currently transitioning');
                    return;
                }

                // Stop current auto-progression and reset current item's progress
                console.log('⏹️ Stopping auto progress and resetting current item progress');
                this.stopAutoProgress();

                // Reset current item's progress fill to 0
                const currentFill = this.progressFills[this.currentIndex];
                if (currentFill) {
                    console.log(`🔄 Resetting progress fill for item ${this.currentIndex}`);
                    currentFill.style.height = '0%';
                }

                // If clicking on the same item, just restart its progress
                if (index === this.currentIndex) {
                    console.log('🔁 Same item clicked, restarting progress');
                    // Restart auto-progression on the same item
                    setTimeout(() => {
                        console.log('▶️ Starting auto progress on same item');
                        this.startAutoProgress();
                    }, 100);
                    return;
                }

                console.log(`🔄 Transitioning from item ${this.currentIndex} to item ${index}`);

                // Transition to clicked item
                this.isTransitioning = true;

                // Fade out current content
                console.log('🌫️ Fading out current content');
                this.fadeOutContent(this.currentIndex);

                // After fade out, transition to clicked item
                setTimeout(() => {
                    console.log('📏 Showing benefit (height only)');
                    this.showBenefit(index, false, false);

                    // After height animation, fade in new content
                    setTimeout(() => {
                        console.log('✨ Fading in new content');
                        this.fadeInContent(index);

                        setTimeout(() => {
                            console.log('✅ Transition complete, starting auto progress');
                            this.isTransitioning = false;

                            // Start auto-progression on the new item
                            this.startAutoProgress();
                        }, 350);
                    }, this.TRANSITION_DURATION);
                }, 350);
            });
        });

        // Pause on hover
        this.container.addEventListener('mouseenter', () => {
            this.clearTimers();
        });

        this.container.addEventListener('mouseleave', () => {
            if (this.isAutoProgressing) {
                this.startAutoProgress();
            }
        });

        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.clearTimers();
            } else if (this.isAutoProgressing) {
                this.startAutoProgress();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.calculateDimensions();
            this.showBenefit(this.currentIndex, false);
        });
    }

    destroy() {
        this.clearTimers();
        // Remove event listeners if needed
    }
}

// Initialize all internship benefits line sections
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.internship-benefits-line');
    const instances = [];

    sections.forEach(section => {
        const instance = new InternshipBenefitsLine(section);
        instances.push(instance);
    });

    // Store instances globally for potential cleanup
    window.internshipBenefitsInstances = instances;
});
