class InternshipBenefitsLine {
    constructor(container) {
        this.container = container;
        this.items = container.querySelectorAll('.benefit-line-item');
        this.descriptions = container.querySelectorAll('.benefit-description');
        this.progressLines = container.querySelectorAll('.progress-line');
        this.progressFills = container.querySelectorAll('.progress-fill');

        this.currentIndex = 0;
        this.isAutoProgressing = true;
        this.isTransitioning = false;
        this.progressTimer = null;
        this.transitionTimer = null;

        this.FILL_DURATION = 4500; // 4.5 seconds
        this.TRANSITION_DURATION = 700; // 0.7 seconds
        this.SPACING = 32; // 32px spacing between items (mb-8)
        this.ICON_SIZE = 36; // w-9 h-9 = 36px
        this.LINE_TOP_OFFSET = 48; // top-12 = 48px (distance from icon top to line start)

        // Calculate proper heights
        this.CLOSED_ITEM_HEIGHT = 105; // Content height for closed items (margin is additional)
        // For closed items: content height - line offset = line height
        this.CLOSED_LINE_HEIGHT = this.CLOSED_ITEM_HEIGHT - this.LINE_TOP_OFFSET; // 105 - 48 = 57px

        this.init();
    }

    init() {
        if (!this.items.length) return;

        // Set up initial state
        this.calculateDimensions();
        this.showBenefit(0, false);
        this.startAutoProgress();

        // Event listeners
        this.setupEventListeners();
    }

    calculateDimensions() {
        this.items.forEach((item, index) => {
            const description = item.querySelector('.benefit-description');
            const line = item.querySelector('.progress-line');

            if (description) {
                // Temporarily show content to measure it
                const wasHidden = description.style.maxHeight === '0px' || description.style.maxHeight === '0';
                if (wasHidden) {
                    description.style.maxHeight = 'none';
                    description.style.visibility = 'hidden';
                }

                const contentHeight = description.scrollHeight;
                // For expanded items: line should extend through the content but stop before next item
                // We need to measure the actual distance from line start to next item and subtract spacing
                const nextItem = this.items[index + 1];
                let expandedLineHeight = this.CLOSED_LINE_HEIGHT;

                if (nextItem) {
                    // Temporarily expand this item to measure the distance
                    const wasHidden = description.style.maxHeight === '0px' || description.style.maxHeight === '0';
                    if (wasHidden) {
                        description.style.maxHeight = `${contentHeight}px`;
                    }

                    // Measure distance from current line start to next item
                    const currentItemRect = item.getBoundingClientRect();
                    const nextItemRect = nextItem.getBoundingClientRect();
                    const distanceToNext = nextItemRect.top - (currentItemRect.top + this.LINE_TOP_OFFSET);
                    expandedLineHeight = Math.max(this.CLOSED_LINE_HEIGHT, distanceToNext - this.SPACING);

                    // Restore state
                    if (wasHidden) {
                        description.style.maxHeight = '0';
                    }
                } else {
                    // Last item: just use content height
                    expandedLineHeight = Math.max(this.CLOSED_LINE_HEIGHT, contentHeight);
                }

                // Store dimensions as CSS custom properties
                description.style.setProperty('--content-height', `${contentHeight}px`);
                if (line) {
                    line.style.setProperty('--line-height-expanded', `${expandedLineHeight}px`);
                    line.style.setProperty('--line-height-closed', `${this.CLOSED_LINE_HEIGHT}px`);
                }

                // Restore original state
                if (wasHidden) {
                    description.style.maxHeight = '0';
                    description.style.visibility = '';
                }
            }
        });
    }

    showBenefit(index, animate = true) {
        if (this.isTransitioning && animate) return;

        this.currentIndex = index;

        // Update descriptions
        this.descriptions.forEach((desc, i) => {
            if (i === index) {
                desc.style.maxHeight = desc.style.getPropertyValue('--content-height') || '200px';
            } else {
                desc.style.maxHeight = '0';
            }
        });

        // Update line heights
        this.progressLines.forEach((line, i) => {
            if (i === index) {
                // Current item: expand line to content height
                line.style.height = line.style.getPropertyValue('--line-height-expanded') || `${this.CLOSED_LINE_HEIGHT}px`;
            } else {
                // Other items: closed height
                line.style.height = line.style.getPropertyValue('--line-height-closed') || `${this.CLOSED_LINE_HEIGHT}px`;
            }
        });

        // Update progress fills
        this.progressFills.forEach((fill, i) => {
            if (i < index) {
                // Completed items: fully filled
                fill.style.height = '100%';
            } else {
                // Current and future items: empty
                fill.style.height = '0%';
            }
        });
    }

    startAutoProgress() {
        if (!this.isAutoProgressing || this.isTransitioning) return;

        this.clearTimers();

        const currentFill = this.progressFills[this.currentIndex];
        if (currentFill) {
            // Start filling the current line
            currentFill.style.height = '100%';

            // Schedule transition to next benefit
            this.progressTimer = setTimeout(() => {
                if (this.isAutoProgressing && !this.isTransitioning) {
                    const nextIndex = (this.currentIndex + 1) % this.items.length;
                    this.transitionToNext(nextIndex);
                }
            }, this.FILL_DURATION);
        }
    }

    transitionToNext(nextIndex) {
        if (this.isTransitioning) return;

        this.isTransitioning = true;
        this.clearTimers();

        // Close current description
        const currentDesc = this.descriptions[this.currentIndex];
        if (currentDesc) {
            currentDesc.style.maxHeight = '0';
        }

        // Shrink current line to closed height
        const currentLine = this.progressLines[this.currentIndex];
        if (currentLine) {
            currentLine.style.height = `${this.CLOSED_LINE_HEIGHT}px`;
        }

        // After closing animation completes
        this.transitionTimer = setTimeout(() => {
            // Open next benefit
            this.showBenefit(nextIndex, false);

            // Wait for opening animation to complete
            setTimeout(() => {
                this.isTransitioning = false;

                // Continue auto-progression after a brief pause
                setTimeout(() => {
                    this.startAutoProgress();
                }, 300);
            }, this.TRANSITION_DURATION);
        }, this.TRANSITION_DURATION);
    }

    stopAutoProgress() {
        this.isAutoProgressing = false;
        this.clearTimers();
    }

    resumeAutoProgress() {
        this.isAutoProgressing = true;
        this.startAutoProgress();
    }

    clearTimers() {
        if (this.progressTimer) {
            clearTimeout(this.progressTimer);
            this.progressTimer = null;
        }
        if (this.transitionTimer) {
            clearTimeout(this.transitionTimer);
            this.transitionTimer = null;
        }
    }

    setupEventListeners() {
        // Click handlers for manual navigation
        this.items.forEach((item, index) => {
            item.addEventListener('click', () => {
                if (this.isTransitioning) return;

                this.stopAutoProgress();
                this.showBenefit(index, true);

                // Resume auto-progression after 5 seconds of inactivity
                setTimeout(() => {
                    this.resumeAutoProgress();
                }, 5000);
            });
        });

        // Pause on hover
        this.container.addEventListener('mouseenter', () => {
            this.clearTimers();
        });

        this.container.addEventListener('mouseleave', () => {
            if (this.isAutoProgressing) {
                this.startAutoProgress();
            }
        });

        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.clearTimers();
            } else if (this.isAutoProgressing) {
                this.startAutoProgress();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.calculateDimensions();
            this.showBenefit(this.currentIndex, false);
        });
    }

    destroy() {
        this.clearTimers();
        // Remove event listeners if needed
    }
}

// Initialize all internship benefits line sections
document.addEventListener('DOMContentLoaded', function() {
    const sections = document.querySelectorAll('.internship-benefits-line');
    const instances = [];

    sections.forEach(section => {
        const instance = new InternshipBenefitsLine(section);
        instances.push(instance);
    });

    // Store instances globally for potential cleanup
    window.internshipBenefitsInstances = instances;
});
