import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    EditorContainer
} from './editor';

registerBlockType('sage/newsletter-form', {
    apiVersion: 2,
    title: 'Newsletter Form',
    icon: 'email',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        placeholder: {
            type: 'string',
            default: '',
        },
        buttonText: {
            type: 'string',
            default: '',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, placeholder, buttonText } = attributes;

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Newsletter Form">
                <TextControl
                    label="Title"
                    value={title}
                    onChange={(value) => setAttributes({ title: value })}
                    placeholder="Enter title"
                    tagName="h2"
                />

                <TextControl
                    label="Description"
                    value={description}
                    onChange={(value) => setAttributes({ description: value })}
                    placeholder="Enter description"
                    tagName="p"
                    multiline={true}
                />

                <TextControl
                    label="Input Placeholder"
                    value={placeholder}
                    onChange={(value) => setAttributes({ placeholder: value })}
                    placeholder="Enter placeholder text"
                    tagName="p"
                />

                <TextControl
                    label="Button Text"
                    value={buttonText}
                    onChange={(value) => setAttributes({ buttonText: value })}
                    placeholder="Enter button text"
                    tagName="p"
                />
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
