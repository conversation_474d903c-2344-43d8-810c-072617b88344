@import 'single.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

.single .h-entry p {
    margin-bottom: 1rem;
}

.single .h-entry ul,
.single .h-entry ol {
    list-style: disc;
    padding-left: 0.75rem;
    margin-bottom: 1rem;
}

.grid-item-blue .subtitle::after {
    background: linear-gradient(90deg, rgba(209, 233, 245, 0) 0%, #6CB9DF 20%, rgba(209, 233, 245, 0) 100%);
}

.grid-item-green .subtitle::after {
    background: linear-gradient(90deg, rgba(169, 223, 108, 0) 0%, #A9DF6C 20%, rgba(169, 223, 108, 0) 100%);
}

.grid-item-pink .subtitle::after {
    background: linear-gradient(90deg, rgba(245, 209, 223, 0) 0%, #DF6C9A 20%, rgba(245, 209, 223, 0) 100%);
}

.grid-item-orange .subtitle::after {
    background: linear-gradient(90deg, rgba(209, 233, 245, 0) 0%, #DF6C6C 20%, rgba(245, 209, 209, 0) 100%);
}

.accordion-item .top::after,
.gray::after {
    background: linear-gradient(90deg, rgba(209, 233, 245, 0) 0%, #B6BBCD 20%, rgba(245, 209, 209, 0) 100%);
}

.vertical::after {
    background: linear-gradient(180deg, rgba(209, 233, 245, 0) 0%, #B6BBCD 20%, rgba(245, 209, 209, 0) 100%);
}

.vertical:last-child:after {
    content: none;
}

.blue-bg {
    background: url('@images/blue_bg.svg') no-repeat center/cover;
}

.blue-dark-bg {
    background: url('@images/blue_dark_bg.svg') no-repeat center/cover;
}

.green-bg {
    background: url('@images/green_bg.svg') no-repeat center/cover;
}

.peach-bg {
    background: url('@images/peach_bg.svg') no-repeat center/cover;
}

.pink-bg {
    background: url('@images/pink_bg.svg') no-repeat center/cover;
}

.gray-bg {
    background: #F2F4F5;
}

.cutout-fade {
    mask-image: url('@images/cutout_shape.svg');
    mask-size: 110%;
    mask-position: 0 20%;
    mask-repeat: no-repeat;
}

.mask-none {
    mask-image: none;
}

.fade-bottom {
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 95%, rgba(0, 0, 0, 0) 100%);
}

.cutout-small {
    mask-image: url('@images/cutout_small.svg');
    mask-size: 1608px;
    mask-position: 100% 100%;
    mask-repeat: no-repeat;
}

.single .post-content img {
    margin: 0;
    border-radius: 1.5rem;
}

.table-of-contents li::before {
    content: '';
    position: absolute;
    width: 0.125rem;
    min-height: 2.5rem;
    background: #CCE9F6;
    left: 0;
    border-radius: 6.25px;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.table-of-contents li.active::before {
    background: #3E9DC5;
}

.enlighter-btn-website {
    display: none !important;
}

.swiper-pagination-bullet {
    background-color: #9AB4CC;
    opacity: 1;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: #73C3EA;
    opacity: 1;
}

.popular-articles-slider .swiper-pagination-bullet {
    background-color: #3F4A5A;
    opacity: 0.25;
}

.popular-articles-slider .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: #222831;
    opacity: 1;
}

.case-studies-section .swiper-pagination,
.contact-form-section .swiper-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    z-index: -1;
}

.case-studies-slider .swiper-pagination-bullet {
    background-color: #fff;
    opacity: 0.25;
}

.case-studies-slider .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
}

.contact-form-section .swiper-pagination {
    z-index: 0;
    bottom: 2rem;
}

.case-studies-section .swiper-pagination-bullet,
.contact-form-section .swiper-pagination-bullet {
    background-color: transparent;
    border: 0.0625rem solid #6E7B91;
    height: 0.5rem;
    width: 0.5rem;
    margin: 0 !important;
}

.case-studies-section .swiper-pagination-bullet.swiper-pagination-bullet-active,
.contact-form-section .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: #222831;
    border-color: #222831;
    height: 0.75rem;
    width: 0.75rem;
}

.dynamic-clamp {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.single-hero-image .cutout-fade {
    mask-position: 0% 0%;
}

.contact-form-section .cutout-fade {
    mask-position: -120px 100%;
    mask-size: 200%;
}

.clamp-1 {
    -webkit-line-clamp: 1 !important;
}

.clamp-2 {
    -webkit-line-clamp: 2 !important;
}

.clamp-3 {
    -webkit-line-clamp: 3 !important;
}

button {
    transition: width 0.3s ease, opacity 0.3s ease;
}

button .fa-spinner {
    opacity: 0;
    display: none;
    animation: spin 1s linear infinite;
}

.loading-button {
    text-align: left;
    justify-content: flex-start;
}

@media (min-width: 1024px) {
    .single-hero-image .cutout-fade {
        mask-position: 0 20%;
    }

    .contact-form-section .cutout-fade {
        mask-position: -70px -130px;
        mask-size: 120%;
    }

    .cutout-fade-desktop {
        mask-image: url('@images/cutout_shape.svg');
        mask-size: 110%;
        mask-position: 0 20%;
        mask-repeat: no-repeat;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Interns section - title with inline images */
.title-container {
    position: relative;
}

.profile-images-container {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

.profile-thumbnail {
    display: inline-block;
}

/* Interns accordion animation */
.intern-content {
    transition: height 0.3s ease-in-out, padding 0.3s ease-in-out, opacity 0.3s ease-in-out;
    box-sizing: border-box;
    will-change: height, opacity, padding;
}

.intern-content .content-inner {
    box-sizing: border-box;
}

.intern-content.closed {
    height: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    border-top: 0;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    opacity: 0;
}

/* Prevent transitions during initial setup */
.intern-content.not-animated {
    transition: none !important;
}

.intern-header {
    transition: background-color 0.3s ease-in-out;
    cursor: pointer;
}

/* Internship Benefits Line Variant Animations */
.internship-benefits-line .benefit-description {
    transition: max-height 0.7s ease-in-out;
    will-change: max-height;
}

.internship-benefits-line .progress-line {
    transition: height 0.7s ease-in-out;
    will-change: height;
}

.internship-benefits-line .progress-fill {
    transition: height var(--fill-duration, 4500ms) ease-linear;
    will-change: height;
}

.internship-benefits-line .benefit-line-item {
    transition: opacity 0.3s ease-in-out;
}

.internship-benefits-line .benefit-line-item:hover {
    opacity: 0.8;
}
