@php
$cat1 = $attributes['category1'] ?? '';
$cat2 = $attributes['category2'] ?? '';
$cat3 = $attributes['category3'] ?? '';
$categories = array_filter([$cat1, $cat2, $cat3]);
$related_posts = [];

foreach ($categories as $cat_id) {
    $query = new WP_Query([
        'posts_per_page' => 1,
        'cat'           => $cat_id,
    ]);
    if ($query->have_posts()) {
        $related_posts[] = $query->posts[0];
    }
    wp_reset_postdata();
}
@endphp

@if(count($related_posts))
    <div class="flex justify-center items-center">
        <div class="container flex flex-col items-start justify-between px-5 py-8 lg:flex-row">
            <div class="mb-4 lg:max-w-md lg:w-3/5 lg:pr-6 lg:mb-0">
                <x-title class="mb-2" variation="large">Related blogs</x-title>
                <p class="text-bodyLarge lg:text-bodyLargeDesktop">
                    Gain hands-on insights into product experience from our experts.
                </p>
            </div>
            <div>
                <x-button type="link" variant="secondary" :href="'/blog'">
                    Read other blogs
                </x-button>
            </div>
        </div>
    </div>

    <div class="container px-5 py-8 lg:py-[124px] mx-auto scroll-smooth">
        <div class="related-posts posts-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($related_posts as $related_post)
                @php
                    setup_postdata($related_post);
                    $post_categories = get_the_category($related_post->ID);
                    $category_slugs = [];
                    foreach ($post_categories as $cat) {
                        $category_slugs[] = $cat->slug;
                    }
                    $permalink = get_permalink($related_post->ID);
                    $excerpt = has_excerpt($related_post->ID)
                        ? wp_strip_all_tags(get_the_excerpt($related_post->ID))
                        : wp_strip_all_tags(get_post_field('post_content', $related_post->ID));
                @endphp
                <div class="post-item flex flex-col bg-[#F4F6FA] rounded-3xl overflow-hidden hover:scale-105 transition-all relative" data-categories="{{ implode(',', $category_slugs) }}">
                    <a class="absolute left-0 top-0 w-full h-full z-10" href="{{ $permalink }}"></a>
                    <div class="post-featured-image relative h-40">
                        <div class="top-5 left-5 rounded-full backdrop-blur-md bg-white/45 px-3 pt-[2px] absolute">
                            {{ get_the_date('d.m.Y', $related_post->ID) }}
                        </div>
                        @if (has_post_thumbnail($related_post->ID))
                            <img class="h-full w-full object-cover" src="{{ get_the_post_thumbnail_url($related_post->ID, 'full') }}" alt="{{ get_the_title($related_post->ID) }}">
                        @endif
                    </div>
                    <div class="p-5 px-5 h-full flex flex-col">
                        @if ($post_categories)
                            <div class="post-categories flex flex-wrap gap-2 mt-2">
                                @foreach($post_categories as $category)
                                    <span class="tag bg-[#E8EBF3] pt-0.5 px-3 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">
                                        {{ $category->name }}
                                    </span>
                                @endforeach
                            </div>
                        @endif
                        <div class="py-2 mt-4">
                            <h2 class="text-bodyLarge mb-1 lg:text-bodyLargeDesktop">
                                <a href="{{ $permalink }}">{!! get_the_title($related_post->ID) !!}</a>
                            </h2>
                            <div class="relative h-10 overflow-hidden text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">
                                <p class="text-bodyExtraSmall h-14 text-[#3F4A5A] lg:text-bodyExtraSmallDesktop" style="-webkit-line-clamp:2;display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;">
                                    {!! $excerpt !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                @php wp_reset_postdata(); @endphp
            @endforeach
        </div>
    </div>
@endif
